# !/usr/bin/python3
# -*- coding: utf-8 -*-
import time

import weeeTest

from wms.qa_config import global_data
from wms.test_dir.api.wms.wms import wms


class TestRestock(weeeTest.TestCase):
    """补货流程测试类"""

    def setup_class(self):
        """测试前准备工作"""
        # 登录系统
        self.account, self.user_name = wms.wms_login.common_login()

        # 初始化补货接口参数
        self.rep_info = globals()

        self.rep_info['warehouse_number'] = global_data.restock['warehouse_number']
        self.rep_info['storage_type'] = global_data.restock['storage_type']
        self.rep_info['itemNumber'] = global_data.restock['item_number']
        self.rep_info['stock_location'] = global_data.restock['stock_location']
        self.rep_info['bin_location'] = global_data.restock['bin_location']
        self.rep_info['curdate'] = time.strftime("%Y-%m-%d", time.localtime())
        self.rep_info['inUser'] = self.user_name + '(' + str(self.account) + ')'

        # 设置仓库和存储类型
        wms.util.update_header(weee_warehouse=self.rep_info['warehouse_number'])
        wms.util.update_header(weee_wms_storage_type=self.rep_info['storage_type'])


    def teardown_class(self):
        """测试后清理工作"""
        # 退出登录
        wms.wms_login.logout(warehouse_number=self.rep_info['warehouse_number'], user_id=self.account, user_name=self.user_name)

    # def _init_restock_api(self):
    #     """初始化补货接口参数"""
    #     wms.restock.storage_type = str(self.storage_type)
    #     wms.restock.warehouse = self.warehouse
    #     wms.restock.userid = self.account
    #     wms.restock.username = self.user_name
    #     wms.restock.user = self.user_name + '(' + str(self.account) + ')'

    def _get_available_pallet(self):
        """获取可用的托盘"""
        return wms.wms_db.get_wh_storage_location_info(
            warehouse=self.rep_info['warehouse_number'],
            location_type=38,
            flag=0,
            info='location_no'
        )

    def _create_restock_task(self,warehouse,user,item_number, stock_location, bin_location):
        """创建补货任务"""

        # 创建补货任务
        response = wms.restock.add_restock_task(
            warehouse=warehouse,
            user=user,
            item_number=item_number,
            delivery_dtm_str=self.rep_info['curdate'],
            stock_location=stock_location,
            bin_location=bin_location,
            recommend_box=1,
            recommend_qty=1
        )

        # 断言：验证任务创建成功
        assert response == "10000", "补货任务创建失败"

        # 查询任务信息并验证
        task_info = wms.wms_db.get_restock_task_infoassert(self.rep_info['itemNumber'],self.rep_info['warehouse_number'],status=0)
        assert task_info is not None, f"无法获取任务 {task_info} 的信息"

    def _get_or_create_restock_task(self, warehouse_number=None):
        """获取或创建补货任务

        Args:
            warehouse_number: 指定仓库编号，如果为None则使用当前仓库
        """
        # 保存原始仓库设置
        original_warehouse = self.rep_info['warehouse_number']
        original_storage_type = self.rep_info['storage_type']

        try:
            # 如果指定了仓库，则切换到指定仓库
            if warehouse_number:
                wms.util.update_header(weee_warehouse=warehouse_number)
                wms.restock.warehouse = warehouse_number

            # 查询当前用户有无未做完的任务
            task = wms.restock.get_tasks(action=0,warehouse_number=self.rep_info['warehouse_number'],user_id=self.account,
                                                        in_user=self.rep_info['inUser'],storage_type=self.rep_info['storage_type'])

            if not task:
                # 尝试创建新的补货任务
                try:
                    self._create_restock_task(warehouse=self.rep_info['warehouse_number'],user=self.account,
                                  item_number=self.rep_info['itemNumber'], stock_location=self.rep_info['stock_location'], bin_location=self.rep_info['bin_location'])
                    # 获取新创建的任务
                    wms.restock.pick_summary(warehouse_number=self.rep_info['warehouse_number'],storage_type=self.rep_info['storage_type'],inuser=self.rep_info['inUser'])
                    task = wms.restock.get_tasks(warehouse_number=self.rep_info['warehouse_number'],user_id=self.account,
                                                        in_user=self.rep_info['inUser'],storage_type=self.rep_info['storage_type'])
                except Exception as e:
                    # 如果创建任务失败，则尝试拉取现有任务
                    body = wms.restock.pick_summary(warehouse_number=self.rep_info['warehouse_number'],storage_type=self.rep_info['storage_type'],inuser=self.rep_info['inUser'])
                    pick_qty = {
                        "groundPick": body["groundPickCount"],
                        "airPick": body["airPickCount"],
                        "fullPallet": body["fullPalletPickCount"]
                    }

                    # 拉取补货任务
                    restock_type = {"groundPick": "1", "airPick": "2", "fullPallet": "3"}
                    pick_type = restock_type[max(pick_qty, key=lambda key: pick_qty[key])]
                    task = wms.restock.get_tasks(pick_type=pick_type,warehouse_number=self.rep_info['warehouse_number'],user_id=self.account,
                                                        in_user=self.rep_info['inUser'],storage_type=self.rep_info['storage_type'])
        finally:
            return task[0] if task else None

    def _execute_pick_process(self, task, pallet):
        """执行拣货流程"""
        # 提取任务信息
        task_id = task["rec_id"]
        stock_location = task["stock_location"]
        recommend_box = task["recommend_box"]
        recommend_qty = task["recommend_qty"]

        # 扫描拣货pallet
        wms.restock.scan_pallet(warehouse_number=self.rep_info['warehouse_number'],user_id=self.account,in_user=self.rep_info['inUser'],pallet=pallet)

        # 根据任务类型执行不同的拣货流程
        if task["lpnNo"] != '':
            # LPN拣货流程
            lpn_list = wms.restock.scan_lpn_location(task_id, stock_location,self.rep_info['warehouse_number'], self.account, self.rep_info['inUser'])["body"]["lpns"]
            pick_lpn = []
            pick_qty = 0
            for lpn in lpn_list:
                if pick_qty + lpn["quantity"] <= recommend_qty:
                    pick_lpn.append(lpn["lpnNo"])
                    pick_qty += lpn["quantity"]
                else:
                    break

            # 确认拣货
            wms.restock.pick_confirm(
                task_id, stock_location, recommend_box,
                recommend_qty, pallet,self.rep_info['warehouse_number'], self.account, self.rep_info['inUser'], lpn_flag=True, lpn_nos=pick_lpn
            )
        else:
          # -----xxx  # 普通拣货流程
            wms.restock.scan_location(task_id, stock_location, self.rep_info['warehouse_number'], self.account, self.rep_info['inUser'])

            # 确认拣货
            wms.restock.pick_confirm(
                task_id, stock_location, recommend_box, recommend_qty, pallet,self.rep_info['warehouse_number'], self.account, self.rep_info['inUser'],  storage_type=self.rep_info['storage_type']
            )

        # 验证pallet与商品绑定
        result = wms.wms_db.get_restock_pallet_item(task_id)
        assert result['pallet_no'] == pallet
        assert result['status'] == 1

        # 完成拣货，释放任务
        wms.restock.pick_complete(pallet,self.rep_info['warehouse_number'], self.account, self.rep_info['inUser'], self.rep_info['storage_type'])

        # 验证拣货完成状态
        result = wms.wms_db.get_restock_pallet_item(task_id)
        assert result['status'] == 2
        result_task = wms.wms_db.get_restock_task_info(task_id)
        assert result_task['status'] == 2

        return task_id

    def _execute_bin_load_process(self, pallet):
        """执行上架流程"""
        # 获取上架任务
        bin_load_task = wms.restock.bin_load_list(pallet,self.rep_info['warehouse_number'],self.account,self.rep_info['inUser'],self.rep_info['storage_type'])["list"]
        if not bin_load_task:
            self.fail("未找到上架任务")

        task = bin_load_task[0]
        task_id = task["rec_id"]
        bin_location = task["bin_sn"]
        upc = task["upc"]
        real_take_box = task["real_take_box"]
        real_take_qty = task["real_take_qty"]
        is_lpn_task = task["isLpnTask"]
        item_number = task["item_number"]
        action = None

        # 扫描商品UPC
        slotting_logic_list = wms.restock.bin_load_list(pallet, self.rep_info['warehouse_number'],
                                                        self.account,self.rep_info['inUser'],self.rep_info['storage_type'], search_code=upc)["slottingLogicList"]

        # 确定上架库位
        if bin_location is None:
            if slotting_logic_list is not None:
                bin_location = slotting_logic_list[0]["locationNO"]
            else:
                stock_list = wms.restock.no_bin_get_stock(item_number, task_id,self.rep_info['warehouse_number'])
                bin_location = stock_list[0]
                action = 4

        # 根据任务类型执行不同的上架流程
        if is_lpn_task:
            # LPN上架流程
            can_scan_lpn_list = wms.restock.lpn_bin_load_detail(
                task_id, pallet, bin_location, action, self.rep_info['warehouse_number']
            )["canScanLpnList"]
            can_scan_lpn_list = list(map(lambda i: i["lpnNo"], can_scan_lpn_list))

            # 确认上架
            if action is None:
                action = 0
            wms.restock.lpn_bin_load_confirm(task_id, pallet, bin_location, can_scan_lpn_list, action, self.rep_info['warehouse_number'], self.account, self.rep_info['inUser'])
        else:
            # ---xxx普通上架流程
            wms.restock.bin_load_get_detail(task_id, pallet, bin_location, action, self.rep_info['warehouse_number'])

            # 确认上架
            wms.restock.bin_load_confirm(task_id, pallet, bin_location, real_take_box, real_take_qty, self.rep_info['warehouse_number'], self.account, self.rep_info['inUser'])

        # 完成上架
        wms.restock.bin_load_complete(pallet, self.rep_info['warehouse_number'], self.rep_info['inUser'],self.account)

        # 验证上架后状态
        result = wms.wms_db.get_restock_pallet_item(task_id)
        assert result['status'] == 3

        # 验证托盘状态
        wms.wms_db.check_tote_status(tote_no=pallet, warehouse=self.rep_info['warehouse_number'], status=0)

        # 验证任务状态
        result_task = wms.wms_db.get_restock_task_info(task_id=task_id)
        assert result_task["status"] == 3

        return task_id

    def test_restock_process(self):
        """
        测试补货流程：Restock pick、binload流程
        """
        # 跳过测试条件：没有补货任务
        if self.task_qty < 1:
            self.skipTest("当前仓库没有补货任务")

        # 准备阶段 (Arrange)
        pallet = self._get_available_pallet()
        task = self._get_or_create_restock_task()

        if not task:
            self.skipTest("无法获取补货任务")

        # 执行阶段 (Act)
        # 1. 执行拣货流程
        task_id = self._execute_pick_process(task, pallet)

        # 2. 执行上架流程
        self._execute_bin_load_process(pallet, task_id)

        # 断言阶段 (Assert)
        # 主要断言已经在各个流程函数中完成

    def test_create_restock_task(self):
        """
        测试创建补货任务
        """
        #创建补货任务
        task = self._get_or_create_restock_task(warehouse_number=self.rep_info['warehouse_number'])
        #执行restock pick流程
        pallet = self._get_available_pallet()
        self._execute_pick_process(task, pallet)
        #执行restock binload流程
        self._execute_bin_load_process(pallet)

        # job生成任务



if __name__ == '__main__':
    weeeTest.main(base_url="https://api.tb1.sayweee.net", debug=True, open=False,case_list=['test_restock_optimized.py::TestRestock::test_create_restock_task'])
